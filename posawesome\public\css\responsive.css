:root {
	/* Base spacing variables */
	--dynamic-xs: 4px;
	--dynamic-sm: 8px;
	--dynamic-md: 16px;
	--dynamic-lg: 24px;
	--dynamic-xl: 32px;

	/* Layout variables */
	--container-height: 75vh;
	--card-height: 60vh;
	--font-scale: 1;

	/* Border radius */
	--border-radius-sm: 4px;
	--border-radius-md: 8px;
	--border-radius-lg: 12px;
	--border-radius-xl: 20px;
	--border-radius-circle: 50%;

	/* Shadows */
	--shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
	--shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
	--shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
	--shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);

	/* Transitions */
	--transition-fast: 0.2s ease;
	--transition-normal: 0.3s ease;
	--transition-slow: 0.5s ease;

	/* Light mode colors */
	--cancel-start: #d32f2f;
	--cancel-end: #c62828;
	--submit-start: #388e3c;
	--submit-end: #2e7d32;
	--primary-start: #1976d2;
	--primary-end: #1565c0;
	--dialog-bg-start: #ffffff;
	--dialog-bg-end: #f8f9fa;
	--dialog-border: #e0e0e0;

	/* Text colors */
	--text-primary: #1a1a1a;
	--text-secondary: #666666;
	--text-disabled: rgba(0, 0, 0, 0.38);

	/* Surface colors */
	--surface-primary: #ffffff;
	--surface-secondary: #f5f5f5;
	--surface-elevated: #ffffff;

	/* Table colors */
	--table-header-bg: #f5f5f5;
	--table-header-text: #333;
	--table-header-border: var(--primary-start);
	--table-row-hover: rgba(25, 118, 210, 0.05);

	/* Form field colors */
	--field-bg: var(--surface-secondary);
	--field-border: #e0e0e0;
	--field-focus: rgba(25, 118, 210, 0.1);
}

/* Ensure the main viewport fills the screen without scroll */
html,
body {
	height: 100%;
	margin: 0;
	padding: 0;
	overflow: hidden;
}

[data-theme="dark"] {
	/* Dark mode colors */
	--cancel-start: #cf6679;
	--cancel-end: #e57373;
	--submit-start: #4caf50;
	--submit-end: #2e7d32;
	--primary-start: #bb86fc;
	--primary-end: #985eff;
	--dialog-bg-start: #1e1e1e;
	--dialog-bg-end: #121212;
	--dialog-border: #373737;

	/* Dark mode palette tokens */
	--background: #121212;
	--surface: #1e1e1e;
	--primary: #bb86fc;
	--primary-variant: #985eff;
	--secondary: #03dac6;
	--error: #cf6679;
	--on-background: #ffffff;
	--on-surface: #ffffff;
	--disabled-text: rgba(255, 255, 255, 0.38);
	--divider: #373737;

	/* Text colors */
	--text-primary: #ffffff;
	--text-secondary: #e0e0e0;
	--text-disabled: rgba(255, 255, 255, 0.38);

	/* Surface colors */
	--surface-primary: #1e1e1e;
	--surface-secondary: #2d2d2d;
	--surface-elevated: #333333;

	/* Table colors */
	--table-header-bg: #2d2d2d;
	--table-header-text: #fff;
	--table-header-border: var(--primary-variant);
	--table-row-hover: rgba(187, 134, 252, 0.1);

	/* Form field colors */
	--field-bg: var(--surface-secondary);
	--field-border: #373737;
	--field-focus: rgba(187, 134, 252, 0.1);
}

/* ===== SPACING UTILITIES ===== */
.dynamic-spacing-xs {
	padding: var(--dynamic-xs);
}

.dynamic-spacing-sm {
	padding: var(--dynamic-sm);
}

.dynamic-spacing-md {
	padding: var(--dynamic-md);
}

.dynamic-spacing-lg {
	padding: var(--dynamic-lg);
}

.dynamic-spacing-xl {
	padding: var(--dynamic-xl);
}

.dynamic-margin-xs {
	margin: var(--dynamic-xs);
}

.dynamic-margin-sm {
	margin: var(--dynamic-sm);
}

.dynamic-margin-md {
	margin: var(--dynamic-md);
}

.dynamic-margin-lg {
	margin: var(--dynamic-lg);
}

.dynamic-margin-xl {
	margin: var(--dynamic-xl);
}

/* Directional spacing utilities */
.dynamic-px-xs {
	padding-left: var(--dynamic-xs);
	padding-right: var(--dynamic-xs);
}

.dynamic-px-sm {
	padding-left: var(--dynamic-sm);
	padding-right: var(--dynamic-sm);
}

.dynamic-px-md {
	padding-left: var(--dynamic-md);
	padding-right: var(--dynamic-md);
}

.dynamic-px-lg {
	padding-left: var(--dynamic-lg);
	padding-right: var(--dynamic-lg);
}

.dynamic-px-xl {
	padding-left: var(--dynamic-xl);
	padding-right: var(--dynamic-xl);
}

.dynamic-py-xs {
	padding-top: var(--dynamic-xs);
	padding-bottom: var(--dynamic-xs);
}

.dynamic-py-sm {
	padding-top: var(--dynamic-sm);
	padding-bottom: var(--dynamic-sm);
}

.dynamic-py-md {
	padding-top: var(--dynamic-md);
	padding-bottom: var(--dynamic-md);
}

.dynamic-py-lg {
	padding-top: var(--dynamic-lg);
	padding-bottom: var(--dynamic-lg);
}

.dynamic-py-xl {
	padding-top: var(--dynamic-xl);
	padding-bottom: var(--dynamic-xl);
}

.dynamic-mx-xs {
	margin-left: var(--dynamic-xs);
	margin-right: var(--dynamic-xs);
}

.dynamic-mx-sm {
	margin-left: var(--dynamic-sm);
	margin-right: var(--dynamic-sm);
}

.dynamic-mx-md {
	margin-left: var(--dynamic-md);
	margin-right: var(--dynamic-md);
}

.dynamic-mx-lg {
	margin-left: var(--dynamic-lg);
	margin-right: var(--dynamic-lg);
}

.dynamic-mx-xl {
	margin-left: var(--dynamic-xl);
	margin-right: var(--dynamic-xl);
}

.dynamic-my-xs {
	margin-top: var(--dynamic-xs);
	margin-bottom: var(--dynamic-xs);
}

.dynamic-my-sm {
	margin-top: var(--dynamic-sm);
	margin-bottom: var(--dynamic-sm);
}

.dynamic-my-md {
	margin-top: var(--dynamic-md);
	margin-bottom: var(--dynamic-md);
}

.dynamic-my-lg {
	margin-top: var(--dynamic-lg);
	margin-bottom: var(--dynamic-lg);
}

.dynamic-my-xl {
	margin-top: var(--dynamic-xl);
	margin-bottom: var(--dynamic-xl);
}

/* ===== TYPOGRAPHY ===== */
.dynamic-text {
	font-size: calc(1rem * var(--font-scale));
}

.dynamic-text-sm {
	font-size: calc(0.875rem * var(--font-scale));
}

.dynamic-text-lg {
	font-size: calc(1.125rem * var(--font-scale));
}

.dynamic-text-xl {
	font-size: calc(1.5rem * var(--font-scale));
}

.text-weight-normal {
	font-weight: 400;
}

.text-weight-medium {
	font-weight: 500;
}

.text-weight-semibold {
	font-weight: 600;
}

.text-weight-bold {
	font-weight: 700;
}

/* ===== COMMON BUTTON STYLES ===== */
.pos-action-btn {
	border-radius: var(--border-radius-lg);
	text-transform: none;
	font-weight: 600;
	padding: 12px 32px;
	min-width: 120px;
	transition: var(--transition-normal);
	color: white;
}

/* Button Variants */
.pos-action-btn.cancel-action-btn {
	background: linear-gradient(135deg, var(--cancel-start) 0%, var(--cancel-end) 100%);
}

.pos-action-btn.submit-action-btn {
	background: linear-gradient(135deg, var(--submit-start) 0%, var(--submit-end) 100%);
}

.pos-action-btn.sync-action-btn,
.pos-action-btn.primary-action-btn {
	background: linear-gradient(135deg, var(--primary-start) 0%, var(--primary-end) 100%);
}

/* Hover Effects */
.pos-action-btn:hover {
	transform: translateY(-2px);
	box-shadow: var(--shadow-lg);
}

.pos-action-btn.cancel-action-btn:hover {
	box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
}

.pos-action-btn.submit-action-btn:hover {
	box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
}

.pos-action-btn.sync-action-btn:hover,
.pos-action-btn.primary-action-btn:hover {
	box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

/* Disabled State */
.pos-action-btn:disabled {
	opacity: 0.6;
	transform: none;
	box-shadow: none;
}

/* Ensure all button text is white */
.v-btn,
.v-btn .v-btn__content,
.v-btn.v-theme--light .v-btn__content,
.v-btn.v-theme--dark .v-btn__content,
.v-btn[color="warning"][theme="dark"] .v-btn__content,
.v-btn[color="primary"][theme="dark"] .v-btn__content,
.v-btn[color="error"][theme="dark"] .v-btn__content,
.v-btn[color="success"][theme="dark"] .v-btn__content,
.v-btn[color="info"][theme="dark"] .v-btn__content,
.v-btn[color="secondary"][theme="dark"] .v-btn__content,
.v-btn[color="accent"][theme="dark"] .v-btn__content {
	color: white;
}

/* ===== COMMON CARD STYLES ===== */
.pos-card {
	border-radius: var(--border-radius-lg);
	overflow: hidden;
	box-shadow: var(--shadow-md);
	transition: var(--transition-normal);
	background-color: var(--surface-primary);
}

.pos-card:hover {
	box-shadow: var(--shadow-lg);
}

/* Card background adjustments */
.cards {
	background-color: var(--surface-secondary);
}

/* Keep cards dark in dark theme */
[data-theme="dark"] .cards,
.v-theme--dark .cards {
	background-color: #121212;
}

/* ===== COMMON TABLE STYLES ===== */
.pos-table {
	border-radius: var(--border-radius-lg);
	overflow: hidden;
	box-shadow: var(--shadow-sm);
	border: 1px solid rgba(0, 0, 0, 0.09);
	margin-bottom: var(--dynamic-md);
	height: 100%;
	display: flex;
	flex-direction: column;
}

.pos-table .v-data-table__wrapper,
.pos-table .v-table__wrapper {
	border-radius: var(--border-radius-sm);
	height: 100%;
	overflow-y: auto;
}

/* Table header styling */
.pos-table th {
	font-weight: 600;
	font-size: 1rem;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	padding: 12px 16px;
	transition: background-color var(--transition-normal);
	border-bottom: 2px solid var(--table-header-border);
	background-color: var(--table-header-bg);
	color: var(--table-header-text);
}

.pos-table .v-data-table-header__content {
	font-weight: 600;
	display: flex;
	justify-content: center;
	align-items: center;
}

.pos-table th:hover {
	background-color: rgba(0, 0, 0, 0.05);
}

.pos-table td {
	padding: 14px 18px;
	height: 64px;
	vertical-align: middle;
}

.pos-table tr:hover {
	background-color: var(--table-row-hover);
}

/* ===== FORM FIELD STYLES ===== */
.pos-form-field {
	border-radius: var(--border-radius-md);
	transition: var(--transition-normal);
	background-color: var(--field-bg);
}

.pos-form-field:hover {
	background-color: var(--field-focus);
}

.pos-form-field input,
.pos-form-field .v-field__input,
.pos-form-field .v-label {
	color: var(--text-primary);
}

.pos-form-field .v-field__overlay {
	background-color: var(--field-bg);
}

/* Background color for fields to match item selector */
.dark-field {
	background-color: var(--surface-secondary) !important;
}

[data-theme="dark"] .dark-field,
.v-theme--dark .dark-field {
	background-color: #1e1e1e !important;
}

/* ===== SLEEK FIELD STYLES ===== */
.sleek-field {
	width: 100%;
	box-sizing: border-box;
}

.sleek-field .v-field {
	border-radius: 12px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	transition: box-shadow 0.3s ease;
	background-color: var(--field-bg);
}

.sleek-field:hover .v-field {
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.sleek-field input,
.sleek-field .v-field__input,
.sleek-field .v-label {
	color: var(--text-primary);
}

.sleek-field .v-field__overlay {
	background-color: var(--field-bg);
}

/* ===== DIALOG STYLES ===== */
.dialog-actions-container {
	background: linear-gradient(135deg, var(--dialog-bg-start) 0%, var(--dialog-bg-end) 100%);
	border-top: 1px solid var(--dialog-border);
	padding: var(--dynamic-md) var(--dynamic-lg);
	gap: var(--dynamic-sm);
}

/* ===== DATE PICKER STYLES ===== */
.custom-date-picker {
	border-radius: var(--border-radius-md);
	overflow: hidden;
	box-shadow: var(--shadow-md);
	max-width: 320px;
	background-color: var(--surface-primary);
	border: 1px solid var(--field-border);
}

.custom-date-picker .v-date-picker-header {
	padding: var(--dynamic-sm);
	background-color: var(--surface-secondary);
	border-bottom: 1px solid var(--field-border);
}

.custom-date-picker .v-date-picker-month {
	padding: var(--dynamic-xs);
}

.custom-date-picker .v-btn {
	margin: 2px;
	min-width: 36px;
	height: 36px;
	border-radius: var(--border-radius-circle);
}

.custom-date-picker .v-btn--active {
	background-color: var(--primary-start);
	color: white;
}

/* ===== THEME TRANSITION ===== */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
	transition: all var(--transition-slow);
	transition-delay: 0;
}

/* ===== DARK MODE OVERRIDES ===== */
/* These styles apply to all dark mode elements */
[data-theme="dark"] .pos-card,
.v-theme--dark .pos-card {
	background-color: var(--surface-primary);
	box-shadow: var(--shadow-lg);
}

[data-theme="dark"] .pos-table th:hover,
.v-theme--dark .pos-table th:hover {
	background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .pos-form-field,
.v-theme--dark .pos-form-field {
	background-color: var(--field-bg);
}

[data-theme="dark"] .pos-form-field input,
[data-theme="dark"] .pos-form-field .v-field__input,
[data-theme="dark"] .pos-form-field .v-label,
.v-theme--dark .pos-form-field input,
.v-theme--dark .pos-form-field .v-field__input,
.v-theme--dark .pos-form-field .v-label {
	color: var(--text-primary);
}

[data-theme="dark"] .pos-form-field .v-field__overlay,
.v-theme--dark .pos-form-field .v-field__overlay {
	background-color: var(--field-bg);
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */

/* Extra Small Devices (Phones, Portrait) */
@media (max-width: 575.98px) {
	:root {
		--dynamic-xs: 6px;
		--dynamic-sm: 12px;
		--dynamic-md: 20px;
		--dynamic-lg: 28px;
		--dynamic-xl: 36px;
		--touch-target-size: 48px;
		--border-radius: 12px;
		--mobile-padding: 8px;
	}

	.dialog-actions-container {
		flex-direction: column;
		gap: var(--dynamic-sm);
		padding: var(--dynamic-md);
	}

	.pos-action-btn {
		width: 100%;
		min-width: unset;
		min-height: var(--touch-target-size);
		font-size: 1.1rem;
		padding: 14px 20px;
	}

	.dynamic-text {
		font-size: calc(1rem * var(--font-scale));
	}

	.dynamic-text-sm {
		font-size: calc(0.9rem * var(--font-scale));
	}

	.dynamic-text-lg {
		font-size: calc(1.2rem * var(--font-scale));
	}

	.dynamic-text-xl {
		font-size: calc(1.4rem * var(--font-scale));
	}

	/* Mobile-specific table styles */
	.pos-table {
		font-size: 0.9rem;
	}

	.pos-table th,
	.pos-table td {
		padding: 8px 12px;
		min-height: var(--touch-target-size);
	}

	/* Mobile form fields */
	.v-field {
		min-height: var(--touch-target-size);
	}

	.v-btn {
		min-height: var(--touch-target-size);
		min-width: var(--touch-target-size);
	}

	/* Hide less important elements on very small screens */
	.hide-on-mobile {
		display: none !important;
	}

	/* Stack elements vertically on mobile */
	.mobile-stack {
		flex-direction: column !important;
	}

	.mobile-stack>* {
		width: 100% !important;
		margin-bottom: var(--dynamic-sm);
	}
}

/* Small Devices (Phones, Landscape) */
@media (min-width: 576px) and (max-width: 767.98px) {
	:root {
		--dynamic-xs: 8px;
		--dynamic-sm: 14px;
		--dynamic-md: 22px;
		--dynamic-lg: 30px;
		--dynamic-xl: 38px;
		--touch-target-size: 46px;
		--border-radius: 10px;
		--mobile-padding: 10px;
	}

	.pos-action-btn {
		min-height: var(--touch-target-size);
		font-size: 1.05rem;
	}

	.dynamic-text {
		font-size: calc(0.98rem * var(--font-scale));
	}

	.dynamic-text-lg {
		font-size: calc(1.15rem * var(--font-scale));
	}

	.dynamic-text-xl {
		font-size: calc(1.35rem * var(--font-scale));
	}
}

/* Medium Devices (Tablets) */
@media (min-width: 768px) and (max-width: 991.98px) {
	:root {
		--touch-target-size: 44px;
		--border-radius: 8px;
		--mobile-padding: 12px;
	}

	.dialog-actions-container {
		flex-direction: row;
		gap: var(--dynamic-md);
	}

	.pos-action-btn {
		min-width: 120px;
		min-height: var(--touch-target-size);
	}

	.dynamic-text {
		font-size: calc(0.96rem * var(--font-scale));
	}

	.dynamic-text-lg {
		font-size: calc(1.1rem * var(--font-scale));
	}

	.dynamic-text-xl {
		font-size: calc(1.32rem * var(--font-scale));
	}

	/* Tablet-specific adjustments */
	.tablet-hide {
		display: none !important;
	}

	.tablet-show {
		display: block !important;
	}
}

/* Large Devices (Desktops) */
@media (min-width: 992px) and (max-width: 1199.98px) {
	.dynamic-text {
		font-size: calc(0.95rem * var(--font-scale));
	}

	.dynamic-text-lg {
		font-size: calc(1.05rem * var(--font-scale));
	}

	.dynamic-text-xl {
		font-size: calc(1.3rem * var(--font-scale));
	}
}

/* Extra Large Devices */
@media (min-width: 1200px) {
	.dynamic-text {
		font-size: calc(1rem * var(--font-scale));
	}

	.dynamic-text-lg {
		font-size: calc(1.125rem * var(--font-scale));
	}

	.dynamic-text-xl {
		font-size: calc(1.5rem * var(--font-scale));
	}
}

/* Orientation-specific styles */
@media (orientation: portrait) and (max-width: 768px) {
	.portrait-stack {
		flex-direction: column !important;
	}

	.portrait-hide {
		display: none !important;
	}

	.portrait-full-width {
		width: 100% !important;
	}
}

@media (orientation: landscape) and (max-width: 768px) {
	.landscape-row {
		flex-direction: row !important;
	}

	.landscape-hide {
		display: none !important;
	}

	/* Adjust heights for landscape mobile */
	:root {
		--container-height: 80vh;
		--card-height: 70vh;
	}
}

/* ===== UTILITY CLASSES ===== */
.border-bottom {
	border-bottom: 1px solid var(--dialog-border);
}

.text-success {
	color: var(--submit-start);
}

.text-secondary {
	color: var(--text-secondary);
}

.disable-events {
	pointer-events: none;
}

/* Allow manual resizing for POS panels */
.resizable {
	resize: vertical;
	overflow: auto;
	min-height: 100px;
}

/* ===== RESPONSIVE UTILITY CLASSES ===== */

/* Display utilities */
.d-mobile-none {
	display: none !important;
}

.d-tablet-none {
	display: none !important;
}

.d-desktop-none {
	display: none !important;
}

@media (max-width: 767.98px) {
	.d-mobile-block {
		display: block !important;
	}

	.d-mobile-flex {
		display: flex !important;
	}

	.d-mobile-none {
		display: none !important;
	}
}

@media (min-width: 768px) and (max-width: 991.98px) {
	.d-tablet-block {
		display: block !important;
	}

	.d-tablet-flex {
		display: flex !important;
	}

	.d-tablet-none {
		display: none !important;
	}
}

@media (min-width: 992px) {
	.d-desktop-block {
		display: block !important;
	}

	.d-desktop-flex {
		display: flex !important;
	}

	.d-desktop-none {
		display: none !important;
	}
}

/* Flex utilities */
.flex-mobile-column {
	flex-direction: column;
}

.flex-mobile-wrap {
	flex-wrap: wrap;
}

@media (max-width: 767.98px) {
	.flex-mobile-column {
		flex-direction: column !important;
	}

	.flex-mobile-row {
		flex-direction: row !important;
	}

	.flex-mobile-wrap {
		flex-wrap: wrap !important;
	}

	.flex-mobile-nowrap {
		flex-wrap: nowrap !important;
	}
}

/* Width utilities */
.w-mobile-100 {
	width: 100%;
}

@media (max-width: 767.98px) {
	.w-mobile-100 {
		width: 100% !important;
	}

	.w-mobile-50 {
		width: 50% !important;
	}

	.w-mobile-auto {
		width: auto !important;
	}
}

/* Spacing utilities for mobile */
@media (max-width: 767.98px) {
	.p-mobile-0 {
		padding: 0 !important;
	}

	.p-mobile-1 {
		padding: var(--dynamic-xs) !important;
	}

	.p-mobile-2 {
		padding: var(--dynamic-sm) !important;
	}

	.p-mobile-3 {
		padding: var(--dynamic-md) !important;
	}

	.m-mobile-0 {
		margin: 0 !important;
	}

	.m-mobile-1 {
		margin: var(--dynamic-xs) !important;
	}

	.m-mobile-2 {
		margin: var(--dynamic-sm) !important;
	}

	.m-mobile-3 {
		margin: var(--dynamic-md) !important;
	}
}

/* Touch-friendly utilities */
.touch-target {
	min-height: var(--touch-target-size);
	min-width: var(--touch-target-size);
}

.touch-padding {
	padding: var(--mobile-padding);
}

/* Scrollable containers for mobile */
.mobile-scroll {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.mobile-scroll::-webkit-scrollbar {
	height: 4px;
}

.mobile-scroll::-webkit-scrollbar-track {
	background: rgba(0, 0, 0, 0.1);
	border-radius: 2px;
}

.mobile-scroll::-webkit-scrollbar-thumb {
	background: rgba(0, 0, 0, 0.3);
	border-radius: 2px;
}

.mobile-scroll::-webkit-scrollbar-thumb:hover {
	background: rgba(0, 0, 0, 0.5);
}

/* Card responsive utilities */
.card-mobile-full {
	border-radius: 0;
	margin: 0;
}

@media (max-width: 767.98px) {
	.card-mobile-full {
		border-radius: 0 !important;
		margin-left: 0 !important;
		margin-right: 0 !important;
	}
}

/* Text size utilities */
.text-mobile-lg {
	font-size: 1.1rem;
}

.text-mobile-xl {
	font-size: 1.2rem;
}

@media (max-width: 767.98px) {
	.text-mobile-lg {
		font-size: 1.1rem !important;
	}

	.text-mobile-xl {
		font-size: 1.2rem !important;
	}

	.text-mobile-sm {
		font-size: 0.9rem !important;
	}
}

/* Enhanced Touch-Friendly Button Styles */
.btn-touch {
	min-height: 48px !important;
	min-width: 48px !important;
	padding: 12px 16px !important;
	font-size: 1rem !important;
	font-weight: 500 !important;
	border-radius: 8px !important;
	transition: all 0.2s ease !important;
}

.btn-touch-sm {
	min-height: 44px !important;
	min-width: 44px !important;
	padding: 10px 14px !important;
	font-size: 0.9rem !important;
}

.btn-touch-lg {
	min-height: 52px !important;
	min-width: 52px !important;
	padding: 14px 20px !important;
	font-size: 1.1rem !important;
}

/* Enhanced Input Field Styles for Touch */
.input-touch {
	font-size: 16px !important;
	/* Prevents zoom on iOS */
	min-height: 48px !important;
	padding: 12px 16px !important;
}

.input-touch .v-field__input {
	min-height: 48px !important;
	padding: 12px 16px !important;
	font-size: 16px !important;
}

/* Enhanced Select Field Styles */
.select-touch {
	font-size: 16px !important;
	min-height: 48px !important;
}

.select-touch .v-field__input {
	min-height: 48px !important;
	font-size: 16px !important;
}

/* Enhanced Checkbox and Radio Styles */
.checkbox-touch .v-selection-control__wrapper {
	min-height: 48px !important;
	min-width: 48px !important;
}

.radio-touch .v-selection-control__wrapper {
	min-height: 48px !important;
	min-width: 48px !important;
}

/* Enhanced Tab Styles */
.tabs-touch .v-tab {
	min-height: 48px !important;
	font-size: 1rem !important;
	font-weight: 500 !important;
	padding: 0 20px !important;
}

/* Enhanced Card Action Styles */
.card-actions-touch {
	padding: 16px !important;
}

.card-actions-touch .v-btn {
	min-height: 44px !important;
	margin: 4px !important;
}

/* Enhanced List Item Styles */
.list-item-touch {
	min-height: 56px !important;
	padding: 12px 16px !important;
}

.list-item-touch .v-list-item__content {
	padding: 8px 0 !important;
}

/* Enhanced Icon Button Styles */
.icon-btn-touch {
	min-height: 48px !important;
	min-width: 48px !important;
	border-radius: 50% !important;
}

.icon-btn-touch .v-icon {
	font-size: 1.5rem !important;
}

/* Enhanced Chip Styles */
.chip-touch {
	min-height: 40px !important;
	padding: 0 16px !important;
	font-size: 0.95rem !important;
}

/* Mobile-specific enhancements */
@media (max-width: 767.98px) {

	/* All buttons become touch-friendly */
	.v-btn:not(.btn-no-touch) {
		min-height: 44px !important;
		min-width: 44px !important;
		font-size: 0.95rem !important;
		padding: 10px 16px !important;
	}

	/* All input fields become touch-friendly */
	.v-text-field input,
	.v-textarea textarea,
	.v-select .v-field__input {
		font-size: 16px !important;
		/* Prevents zoom on iOS */
		min-height: 44px !important;
	}

	/* All form controls get better spacing */
	.v-text-field,
	.v-textarea,
	.v-select,
	.v-autocomplete {
		margin-bottom: 16px !important;
	}

	/* Enhanced touch targets for checkboxes and radios */
	.v-checkbox .v-selection-control__wrapper,
	.v-radio .v-selection-control__wrapper {
		min-height: 44px !important;
		min-width: 44px !important;
	}

	/* Enhanced tab heights */
	.v-tabs .v-tab {
		min-height: 48px !important;
		font-size: 1rem !important;
	}

	/* Enhanced list items */
	.v-list-item {
		min-height: 52px !important;
		padding: 12px 16px !important;
	}

	/* Enhanced card actions */
	.v-card-actions {
		padding: 16px !important;
	}

	/* Enhanced dialog actions */
	.v-card-actions .v-btn {
		min-height: 44px !important;
		margin: 4px 8px !important;
	}

	/* Enhanced menu items */
	.v-menu .v-list-item {
		min-height: 48px !important;
		padding: 12px 20px !important;
	}

	/* Enhanced navigation items */
	.v-navigation-drawer .v-list-item {
		min-height: 52px !important;
		padding: 12px 20px !important;
	}

	/* Enhanced toolbar buttons */
	.v-toolbar .v-btn {
		min-height: 44px !important;
		min-width: 44px !important;
	}

	/* Enhanced data table actions */
	.v-data-table .v-btn {
		min-height: 40px !important;
		min-width: 40px !important;
	}
}

/* Tablet-specific enhancements */
@media (min-width: 768px) and (max-width: 991.98px) {
	.v-btn:not(.btn-no-touch) {
		min-height: 42px !important;
		font-size: 0.9rem !important;
		padding: 8px 14px !important;
	}

	.v-text-field input,
	.v-textarea textarea,
	.v-select .v-field__input {
		font-size: 15px !important;
		min-height: 42px !important;
	}

	.v-tabs .v-tab {
		min-height: 44px !important;
		font-size: 0.95rem !important;
	}

	.v-list-item {
		min-height: 48px !important;
		padding: 10px 16px !important;
	}
}

/* Advanced Media Queries with Orientation Support */

/* Extra Small Devices (Portrait Phones) */
@media (max-width: 575.98px) and (orientation: portrait) {
	:root {
		--container-padding: 8px;
		--card-padding: 12px;
		--button-height: 44px;
		--input-height: 44px;
		--font-size-base: 14px;
		--font-size-small: 12px;
		--font-size-large: 16px;
	}

	.v-container {
		padding: var(--container-padding) !important;
	}

	.v-card {
		border-radius: 8px !important;
		margin: 4px 0 !important;
	}

	.v-card-text {
		padding: var(--card-padding) !important;
	}

	/* Stack elements vertically */
	.v-row.mobile-stack>.v-col {
		flex: 0 0 100% !important;
		max-width: 100% !important;
	}

	/* Hide less important elements */
	.mobile-hide {
		display: none !important;
	}

	/* Full width elements */
	.mobile-full-width {
		width: 100% !important;
		margin-left: 0 !important;
		margin-right: 0 !important;
	}
}

/* Extra Small Devices (Landscape Phones) */
@media (max-width: 767.98px) and (orientation: landscape) {
	:root {
		--container-padding: 12px;
		--card-padding: 16px;
		--button-height: 40px;
		--input-height: 40px;
		--font-size-base: 13px;
		--font-size-small: 11px;
		--font-size-large: 15px;
	}

	/* Optimize for landscape */
	.v-container {
		padding: var(--container-padding) 16px !important;
	}

	/* Reduce vertical spacing in landscape */
	.v-card {
		margin: 2px 0 !important;
	}

	.v-card-text {
		padding: 12px 16px !important;
	}

	/* Two-column layout for landscape phones */
	.landscape-two-col>.v-col {
		flex: 0 0 50% !important;
		max-width: 50% !important;
	}
}

/* Small Devices (Portrait Tablets) */
@media (min-width: 576px) and (max-width: 767.98px) and (orientation: portrait) {
	:root {
		--container-padding: 16px;
		--card-padding: 20px;
		--button-height: 42px;
		--input-height: 42px;
		--font-size-base: 14px;
		--font-size-small: 12px;
		--font-size-large: 16px;
	}

	.v-container {
		padding: var(--container-padding) !important;
	}

	.v-card {
		border-radius: 12px !important;
		margin: 8px 0 !important;
	}

	/* Two-column layout for small tablets */
	.tablet-small-two-col>.v-col {
		flex: 0 0 50% !important;
		max-width: 50% !important;
	}
}

/* Medium Devices (Landscape Tablets) */
@media (min-width: 768px) and (max-width: 991.98px) and (orientation: landscape) {
	:root {
		--container-padding: 20px;
		--card-padding: 24px;
		--button-height: 40px;
		--input-height: 40px;
		--font-size-base: 14px;
		--font-size-small: 12px;
		--font-size-large: 16px;
	}

	.v-container {
		padding: var(--container-padding) !important;
	}

	.v-card {
		border-radius: 12px !important;
		margin: 8px 0 !important;
	}

	/* Three-column layout for landscape tablets */
	.tablet-landscape-three-col>.v-col {
		flex: 0 0 33.333333% !important;
		max-width: 33.333333% !important;
	}
}

/* Large Devices (Portrait Tablets and Small Desktops) */
@media (min-width: 768px) and (max-width: 991.98px) and (orientation: portrait) {
	:root {
		--container-padding: 24px;
		--card-padding: 28px;
		--button-height: 38px;
		--input-height: 38px;
		--font-size-base: 14px;
		--font-size-small: 12px;
		--font-size-large: 16px;
	}

	.v-container {
		padding: var(--container-padding) !important;
	}

	.v-card {
		border-radius: 16px !important;
		margin: 12px 0 !important;
	}
}

/* Extra Large Devices (Large Desktops) */
@media (min-width: 992px) and (max-width: 1199.98px) {
	:root {
		--container-padding: 32px;
		--card-padding: 32px;
		--button-height: 36px;
		--input-height: 36px;
		--font-size-base: 14px;
		--font-size-small: 12px;
		--font-size-large: 16px;
	}

	.v-container {
		padding: var(--container-padding) !important;
	}

	.v-card {
		border-radius: 16px !important;
		margin: 16px 0 !important;
	}

	/* Four-column layout for large desktops */
	.desktop-four-col>.v-col {
		flex: 0 0 25% !important;
		max-width: 25% !important;
	}
}

/* Extra Extra Large Devices (Extra Large Desktops) */
@media (min-width: 1200px) {
	:root {
		--container-padding: 40px;
		--card-padding: 40px;
		--button-height: 36px;
		--input-height: 36px;
		--font-size-base: 14px;
		--font-size-small: 12px;
		--font-size-large: 16px;
	}

	.v-container {
		padding: var(--container-padding) !important;
	}

	.v-card {
		border-radius: 20px !important;
		margin: 20px 0 !important;
	}

	/* Six-column layout for extra large desktops */
	.desktop-xl-six-col>.v-col {
		flex: 0 0 16.666667% !important;
		max-width: 16.666667% !important;
	}
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

	/* Enhance text rendering on high DPI displays */
	body {
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	/* Sharper borders on high DPI */
	.v-card,
	.v-btn,
	.v-text-field {
		border-width: 0.5px;
	}
}

/* Print Styles */
@media print {

	/* Hide navigation and non-essential elements */
	.v-navigation-drawer,
	.v-app-bar,
	.v-footer,
	.v-fab,
	.print-hide {
		display: none !important;
	}

	/* Optimize for print */
	.v-main {
		padding: 0 !important;
	}

	.v-card {
		box-shadow: none !important;
		border: 1px solid #ddd !important;
		margin: 8px 0 !important;
	}

	/* Ensure text is readable */
	body {
		color: #000 !important;
		background: #fff !important;
	}

	/* Page breaks */
	.page-break-before {
		page-break-before: always;
	}

	.page-break-after {
		page-break-after: always;
	}

	.page-break-inside-avoid {
		page-break-inside: avoid;
	}
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {

	*,
	*::before,
	*::after {
		animation-duration: 0.01ms !important;
		animation-iteration-count: 1 !important;
		transition-duration: 0.01ms !important;
		scroll-behavior: auto !important;
	}
}

/* Dark Mode Preferences */
@media (prefers-color-scheme: dark) {
	:root {
		--surface-primary: #121212;
		--surface-secondary: #1e1e1e;
		--surface-tertiary: #2d2d2d;
		--text-primary: #ffffff;
		--text-secondary: #b3b3b3;
		--border-color: #404040;
	}
}

/* Light Mode Preferences */
@media (prefers-color-scheme: light) {
	:root {
		--surface-primary: #ffffff;
		--surface-secondary: #f5f5f5;
		--surface-tertiary: #eeeeee;
		--text-primary: #212121;
		--text-secondary: #757575;
		--border-color: #e0e0e0;
	}
}

/* Hover Capability Detection */
@media (hover: hover) {

	/* Enhanced hover effects for devices that support hover */
	.v-btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
	}

	.v-card:hover {
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
	}

	.v-list-item:hover {
		background-color: rgba(0, 0, 0, 0.04);
	}
}

@media (hover: none) {

	/* Remove hover effects for touch devices */
	.v-btn:hover,
	.v-card:hover,
	.v-list-item:hover {
		transform: none;
		box-shadow: inherit;
		background-color: inherit;
	}
}

/* Enhanced Dialog and Modal Styles */

/* Base dialog improvements */
.v-dialog {
	/* Ensure dialogs are properly centered */
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

.v-dialog .v-card {
	/* Improve dialog card appearance */
	border-radius: 16px !important;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
	overflow: hidden !important;
}

.v-dialog .v-card-title {
	/* Enhanced dialog title */
	padding: 20px 24px 16px !important;
	font-size: 1.25rem !important;
	font-weight: 600 !important;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.v-dialog .v-card-text {
	/* Enhanced dialog content */
	padding: 20px 24px !important;
	line-height: 1.6 !important;
}

.v-dialog .v-card-actions {
	/* Enhanced dialog actions */
	padding: 16px 24px 20px !important;
	border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
	gap: 12px !important;
}

/* Mobile dialog styles */
@media (max-width: 767.98px) {
	.v-dialog {
		/* Full screen dialogs on mobile */
		margin: 0 !important;
		padding: 0 !important;
	}

	.v-dialog .v-card {
		/* Mobile dialog card */
		width: 100vw !important;
		height: 100vh !important;
		max-width: 100vw !important;
		max-height: 100vh !important;
		border-radius: 0 !important;
		margin: 0 !important;
		display: flex !important;
		flex-direction: column !important;
	}

	.v-dialog .v-card-title {
		/* Mobile dialog title */
		padding: 16px 20px !important;
		font-size: 1.1rem !important;
		background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%) !important;
		border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
		flex-shrink: 0 !important;
	}

	.v-dialog .v-card-text {
		/* Mobile dialog content */
		padding: 16px 20px !important;
		flex: 1 !important;
		overflow-y: auto !important;
		-webkit-overflow-scrolling: touch !important;
	}

	.v-dialog .v-card-actions {
		/* Mobile dialog actions */
		padding: 16px 20px !important;
		border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
		flex-shrink: 0 !important;
		background: rgba(248, 249, 250, 0.8) !important;
	}

	.v-dialog .v-card-actions .v-btn {
		/* Mobile dialog buttons */
		min-height: 44px !important;
		flex: 1 !important;
		margin: 0 4px !important;
		font-size: 1rem !important;
	}

	/* Mobile dialog close button */
	.v-dialog .v-card-title .v-btn {
		min-height: 44px !important;
		min-width: 44px !important;
	}
}

/* Tablet dialog styles */
@media (min-width: 768px) and (max-width: 991.98px) {
	.v-dialog .v-card {
		/* Tablet dialog sizing */
		max-width: 90vw !important;
		max-height: 90vh !important;
		width: auto !important;
		height: auto !important;
	}

	.v-dialog .v-card-title {
		/* Tablet dialog title */
		padding: 18px 22px !important;
		font-size: 1.15rem !important;
	}

	.v-dialog .v-card-text {
		/* Tablet dialog content */
		padding: 18px 22px !important;
		max-height: 60vh !important;
		overflow-y: auto !important;
	}

	.v-dialog .v-card-actions {
		/* Tablet dialog actions */
		padding: 16px 22px 18px !important;
	}

	.v-dialog .v-card-actions .v-btn {
		/* Tablet dialog buttons */
		min-height: 42px !important;
		padding: 8px 16px !important;
		font-size: 0.95rem !important;
	}
}

/* Desktop dialog styles */
@media (min-width: 992px) {
	.v-dialog .v-card {
		/* Desktop dialog sizing */
		max-width: 80vw !important;
		max-height: 80vh !important;
		min-width: 400px !important;
	}

	.v-dialog .v-card-text {
		/* Desktop dialog content */
		max-height: 60vh !important;
		overflow-y: auto !important;
	}
}

/* Special dialog types */
.dialog-fullscreen {
	/* Fullscreen dialog utility */
	width: 100vw !important;
	height: 100vh !important;
	max-width: 100vw !important;
	max-height: 100vh !important;
	border-radius: 0 !important;
}

.dialog-bottom-sheet {
	/* Bottom sheet dialog for mobile */
	align-self: flex-end !important;
	width: 100% !important;
	max-height: 80vh !important;
	border-radius: 16px 16px 0 0 !important;
	margin: 0 !important;
}

.dialog-center-small {
	/* Small centered dialog */
	max-width: 400px !important;
	width: 90vw !important;
}

.dialog-center-medium {
	/* Medium centered dialog */
	max-width: 600px !important;
	width: 90vw !important;
}

.dialog-center-large {
	/* Large centered dialog */
	max-width: 800px !important;
	width: 90vw !important;
}

/* Dialog animations */
.dialog-slide-up-enter-active,
.dialog-slide-up-leave-active {
	transition: all 0.3s ease !important;
}

.dialog-slide-up-enter-from {
	transform: translateY(100%) !important;
	opacity: 0 !important;
}

.dialog-slide-up-leave-to {
	transform: translateY(100%) !important;
	opacity: 0 !important;
}

.dialog-fade-enter-active,
.dialog-fade-leave-active {
	transition: all 0.2s ease !important;
}

.dialog-fade-enter-from,
.dialog-fade-leave-to {
	opacity: 0 !important;
	transform: scale(0.9) !important;
}

/* Enhanced Dialog Component Styles */

/* Mobile dialog specific styles */
.mobile-dialog-card {
	display: flex !important;
	flex-direction: column !important;
	height: 100vh !important;
	border-radius: 0 !important;
}

.mobile-dialog-title {
	background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%) !important;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
	flex-shrink: 0 !important;
	padding: 16px 20px !important;
	display: flex !important;
	align-items: center !important;
}

.mobile-dialog-content {
	flex: 1 !important;
	overflow-y: auto !important;
	-webkit-overflow-scrolling: touch !important;
	padding: 16px 20px !important;
}

.mobile-dialog-actions {
	flex-shrink: 0 !important;
	background: rgba(248, 249, 250, 0.8) !important;
	border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
	padding: 16px 20px !important;
	display: flex !important;
	gap: 12px !important;
}

.mobile-dialog-actions .v-btn {
	flex: 1 !important;
	min-height: 44px !important;
	font-size: 1rem !important;
}

.mobile-container {
	padding: 0 !important;
}

.mobile-search-section {
	padding: 0 !important;
}

/* Touch-friendly input and button styles */
.input-touch {
	min-height: 48px !important;
}

.input-touch .v-field__input {
	min-height: 48px !important;
	font-size: 16px !important;
	/* Prevent iOS zoom */
}

.btn-touch {
	min-height: 44px !important;
	min-width: 44px !important;
	font-size: 1rem !important;
}

.touch-target {
	min-height: 44px !important;
	min-width: 44px !important;
}

/* Data table responsive styles for dialogs */
@media (max-width: 767.98px) {
	.v-dialog .v-data-table {
		font-size: 0.875rem !important;
	}

	.v-dialog .v-data-table .v-data-table__td {
		padding: 8px 4px !important;
		font-size: 0.8rem !important;
	}

	.v-dialog .v-data-table .v-data-table__th {
		padding: 8px 4px !important;
		font-size: 0.85rem !important;
		font-weight: 600 !important;
	}

	.v-dialog .v-data-table-header {
		background: rgba(0, 0, 0, 0.05) !important;
	}

	/* Make table horizontally scrollable */
	.v-dialog .v-table {
		overflow-x: auto !important;
		-webkit-overflow-scrolling: touch !important;
	}

	.v-dialog .v-table__wrapper {
		overflow-x: auto !important;
	}
}

/* Dialog overlay improvements */
.v-overlay__content {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

@media (max-width: 767.98px) {
	.v-overlay__content {
		align-items: stretch !important;
		padding: 0 !important;
	}
}

/* Snackbar responsive positioning */
@media (max-width: 767.98px) {
	.v-snackbar {
		bottom: 80px !important;
		/* Above mobile navigation */
		margin: 0 16px !important;
		max-width: calc(100vw - 32px) !important;
	}
}

/* Loading states for dialogs */
.dialog-loading {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	min-height: 200px !important;
}

.dialog-loading .v-progress-circular {
	margin: 20px !important;
}

/* Enhanced form styling in dialogs */
.v-dialog .v-text-field,
.v-dialog .v-select,
.v-dialog .v-autocomplete {
	margin-bottom: 16px !important;
}

@media (max-width: 767.98px) {

	.v-dialog .v-text-field,
	.v-dialog .v-select,
	.v-dialog .v-autocomplete {
		margin-bottom: 20px !important;
	}

	.v-dialog .v-text-field .v-field__input,
	.v-dialog .v-select .v-field__input,
	.v-dialog .v-autocomplete .v-field__input {
		font-size: 16px !important;
		/* Prevent iOS zoom */
		min-height: 48px !important;
	}
}