{"actions": [], "creation": "2023-06-12 02:59:56.133380", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["payment_entry", "posting_date", "column_break_3", "customer", "paid_amount", "mode_of_payment"], "fields": [{"fieldname": "payment_entry", "fieldtype": "Link", "in_list_view": 1, "label": "Payment Entry", "options": "Payment Entry", "reqd": 1}, {"fetch_from": "pos_invoice.posting_date", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "pos_invoice.customer", "fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer", "read_only": 1, "reqd": 1}, {"fetch_from": "pos_invoice.grand_total", "fieldname": "paid_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "reqd": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Data", "label": "Mode Of Payment", "reqd": 1}], "istable": 1, "links": [], "modified": "2023-06-12 02:59:56.133380", "modified_by": "Administrator", "module": "POSAwesome", "name": "POS Payment Entry Reference", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}