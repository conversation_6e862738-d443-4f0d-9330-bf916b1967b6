{"actions": [], "allow_copy": 1, "autoname": "POSA-CS-.YY.-.#######", "creation": "2020-09-29 02:38:20.917434", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["period_start_date", "period_end_date", "column_break_3", "posting_date", "pos_opening_shift", "section_break_5", "company", "column_break_7", "pos_profile", "user", "section_break_12", "pos_transactions", "section_break_9", "pos_payments", "section_break_if3m1", "payment_reconciliation_details", "section_break_11", "payment_reconciliation", "section_break_13", "grand_total", "net_total", "total_quantity", "column_break_16", "taxes", "section_break_14", "amended_from"], "fields": [{"fetch_from": "pos_opening_shift.period_start_date", "fieldname": "period_start_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Period Start Date", "read_only": 1, "reqd": 1}, {"default": "Today", "fieldname": "period_end_date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Period End Date", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Posting Date", "reqd": 1}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fetch_from": "pos_opening_shift.pos_profile", "fieldname": "pos_profile", "fieldtype": "Link", "in_list_view": 1, "label": "POS Profile", "options": "POS Profile", "reqd": 1}, {"fetch_from": "pos_opening_shift.user", "fieldname": "user", "fieldtype": "Link", "label": "Cashier", "options": "User", "reqd": 1}, {"fieldname": "section_break_12", "fieldtype": "Section Break", "label": "Linked Invoices"}, {"fieldname": "pos_transactions", "fieldtype": "Table", "label": "POS Transactions", "options": "Sales Invoice Reference"}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "read_only": 1}, {"depends_on": "eval:doc.docstatus==1", "fieldname": "payment_reconciliation_details", "fieldtype": "HTML"}, {"fieldname": "section_break_11", "fieldtype": "Section Break", "label": "Modes of Payment"}, {"fieldname": "payment_reconciliation", "fieldtype": "Table", "label": "Payment Reconciliation", "options": "POS Closing Shift Detail"}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.docstatus==0", "fieldname": "section_break_13", "fieldtype": "Section Break", "label": "Details"}, {"default": "0", "fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "read_only": 1}, {"default": "0", "fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "read_only": 1}, {"fieldname": "total_quantity", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Taxes", "options": "POS Closing Shift Taxes", "read_only": 1}, {"fieldname": "section_break_14", "fieldtype": "Section Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "POS Closing Shift", "print_hide": 1, "read_only": 1}, {"fieldname": "pos_opening_shift", "fieldtype": "Link", "label": "POS Opening Shift", "options": "POS Opening Shift", "reqd": 1}, {"fieldname": "pos_payments", "fieldtype": "Table", "label": "POS Payments", "options": "POS Payment Entry Reference"}, {"fieldname": "section_break_if3m1", "fieldtype": "Section Break"}], "is_submittable": 1, "links": [], "modified": "2023-06-12 03:01:49.146706", "modified_by": "Administrator", "module": "POSAwesome", "name": "POS Closing Shift", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}