{"actions": [], "autoname": "field:mpesa_settings", "creation": "2021-11-10 03:59:38.274817", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["mpesa_settings", "till_number", "business_shortcode", "column_break_4", "company", "mode_of_payment", "register_status"], "fields": [{"fieldname": "mpesa_settings", "fieldtype": "Link", "in_list_view": 1, "label": "Mpesa Settings", "options": "Mpesa Settings", "reqd": 1, "unique": 1}, {"fetch_from": "mpesa_settings.till_number", "fieldname": "till_number", "fieldtype": "Data", "label": "Till Number", "read_only": 1}, {"fetch_from": "mpesa_settings.business_shortcode", "fieldname": "business_shortcode", "fieldtype": "Data", "label": "Business Shortcode", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Mode of Payment", "options": "Mode of Payment", "reqd": 1}, {"default": "Pending", "fieldname": "register_status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Register Status", "options": "Pending\nSuccess\nFailed", "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "options": "Company", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-06-07 17:56:04.569137", "modified_by": "Administrator", "module": "POSAwesome", "name": "Mpesa C2B Register URL", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}