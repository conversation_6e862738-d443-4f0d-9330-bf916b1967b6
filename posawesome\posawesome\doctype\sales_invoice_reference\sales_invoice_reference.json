{"actions": [], "creation": "2020-09-29 02:58:12.486831", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["sales_invoice", "posting_date", "column_break_3", "customer", "grand_total"], "fields": [{"fieldname": "sales_invoice", "fieldtype": "Link", "in_list_view": 1, "label": "Sales Invoice", "options": "Sales Invoice", "reqd": 1}, {"fetch_from": "pos_invoice.posting_date", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fetch_from": "pos_invoice.customer", "fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "label": "Customer", "options": "Customer", "read_only": 1, "reqd": 1}, {"fetch_from": "pos_invoice.grand_total", "fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "reqd": 1}], "istable": 1, "links": [], "modified": "2023-06-12 02:57:24.861219", "modified_by": "Administrator", "module": "POSAwesome", "name": "Sales Invoice Reference", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}