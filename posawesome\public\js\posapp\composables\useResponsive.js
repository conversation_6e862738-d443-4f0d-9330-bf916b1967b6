import { ref, computed, onMounted, onBeforeUnmount } from "vue";

// Enhanced responsive breakpoints for better mobile and tablet support
const BREAKPOINTS = {
    xs: 0,      // Extra small devices (phones, 0px and up)
    sm: 576,    // Small devices (landscape phones, 576px and up)
    md: 768,    // Medium devices (tablets, 768px and up)
    lg: 992,    // Large devices (desktops, 992px and up)
    xl: 1200,   // Extra large devices (large desktops, 1200px and up)
    xxl: 1400   // Extra extra large devices (larger desktops, 1400px and up)
};

export function useResponsive() {
    const windowWidth = ref(window.innerWidth);
    const windowHeight = ref(window.innerHeight);
    const baseWidth = ref(1920); // Standard desktop base width
    const baseHeight = ref(1080); // Standard desktop base height

    // Device type detection
    const isMobile = computed(() => windowWidth.value < BREAKPOINTS.md);
    const isTablet = computed(() => windowWidth.value >= BREAKPOINTS.md && windowWidth.value < BREAKPOINTS.lg);
    const isDesktop = computed(() => windowWidth.value >= BREAKPOINTS.lg);
    const isLandscape = computed(() => windowWidth.value > windowHeight.value);
    const isPortrait = computed(() => windowHeight.value > windowWidth.value);

    // Current breakpoint detection
    const currentBreakpoint = computed(() => {
        const width = windowWidth.value;
        if (width < BREAKPOINTS.sm) return 'xs';
        if (width < BREAKPOINTS.md) return 'sm';
        if (width < BREAKPOINTS.lg) return 'md';
        if (width < BREAKPOINTS.xl) return 'lg';
        if (width < BREAKPOINTS.xxl) return 'xl';
        return 'xxl';
    });

    // Enhanced scaling calculations
    const widthScale = computed(() => {
        // Use different scaling logic for mobile vs desktop
        if (isMobile.value) {
            return Math.min(1.2, windowWidth.value / 375); // Base mobile width 375px
        } else if (isTablet.value) {
            return Math.min(1.1, windowWidth.value / 768); // Base tablet width 768px
        }
        return windowWidth.value / baseWidth.value;
    });

    const heightScale = computed(() => {
        if (isMobile.value) {
            return Math.min(1.2, windowHeight.value / 667); // Base mobile height 667px
        } else if (isTablet.value) {
            return Math.min(1.1, windowHeight.value / 1024); // Base tablet height 1024px
        }
        return windowHeight.value / baseHeight.value;
    });

    const averageScale = computed(() => (widthScale.value + heightScale.value) / 2);

    // Enhanced dynamic spacing with device-specific adjustments
    const dynamicSpacing = computed(() => {
        const baseSpacing = {
            xs: isMobile.value ? 6 : 4,
            sm: isMobile.value ? 12 : 8,
            md: isMobile.value ? 20 : 16,
            lg: isMobile.value ? 28 : 24,
            xl: isMobile.value ? 36 : 32,
        };

        const scale = isMobile.value ? Math.max(0.8, averageScale.value) : averageScale.value;

        return {
            xs: Math.max(isMobile.value ? 4 : 2, Math.round(baseSpacing.xs * scale)),
            sm: Math.max(isMobile.value ? 8 : 4, Math.round(baseSpacing.sm * scale)),
            md: Math.max(isMobile.value ? 12 : 8, Math.round(baseSpacing.md * scale)),
            lg: Math.max(isMobile.value ? 16 : 12, Math.round(baseSpacing.lg * scale)),
            xl: Math.max(isMobile.value ? 20 : 16, Math.round(baseSpacing.xl * scale)),
        };
    });

    // Enhanced responsive styles with better mobile support
    const responsiveStyles = computed(() => {
        let cardHeightVh;
        let containerHeightVh;
        let fontScale;

        if (isMobile.value) {
            // Mobile-specific calculations
            cardHeightVh = isPortrait.value ?
                Math.round(40 * heightScale.value) :
                Math.round(50 * heightScale.value);
            containerHeightVh = isPortrait.value ?
                Math.round(60 * heightScale.value) :
                Math.round(70 * heightScale.value);
            fontScale = Math.max(1.0, Math.min(1.3, averageScale.value));
        } else if (isTablet.value) {
            // Tablet-specific calculations
            cardHeightVh = isPortrait.value ?
                Math.round(50 * heightScale.value) :
                Math.round(60 * heightScale.value);
            containerHeightVh = Math.round(70 * heightScale.value);
            fontScale = Math.max(0.9, Math.min(1.2, averageScale.value));
        } else {
            // Desktop calculations
            cardHeightVh = Math.round(60 * heightScale.value);
            containerHeightVh = Math.round(75 * heightScale.value);
            fontScale = Math.max(0.8, Math.min(1.1, averageScale.value));
        }

        // Ensure reasonable bounds
        cardHeightVh = Math.max(30, Math.min(cardHeightVh, 80));
        containerHeightVh = Math.max(40, Math.min(containerHeightVh, 90));

        return {
            "--dynamic-xs": `${dynamicSpacing.value.xs}px`,
            "--dynamic-sm": `${dynamicSpacing.value.sm}px`,
            "--dynamic-md": `${dynamicSpacing.value.md}px`,
            "--dynamic-lg": `${dynamicSpacing.value.lg}px`,
            "--dynamic-xl": `${dynamicSpacing.value.xl}px`,
            "--container-height": `${containerHeightVh}vh`,
            "--card-height": `${cardHeightVh}vh`,
            "--font-scale": fontScale.toFixed(2),
            "--mobile-padding": isMobile.value ? "8px" : "16px",
            "--touch-target-size": isMobile.value ? "48px" : "40px",
            "--border-radius": isMobile.value ? "12px" : "8px",
        };
    });

    const handleResize = () => {
        windowWidth.value = window.innerWidth;
        windowHeight.value = window.innerHeight;
    };

    // Debounced resize handler for better performance
    let resizeTimeout;
    const debouncedHandleResize = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 100);
    };

    onMounted(() => {
        handleResize();
        window.addEventListener("resize", debouncedHandleResize);
        window.addEventListener("orientationchange", () => {
            // Handle orientation change with delay to get correct dimensions
            setTimeout(handleResize, 200);
        });
    });

    onBeforeUnmount(() => {
        window.removeEventListener("resize", debouncedHandleResize);
        window.removeEventListener("orientationchange", handleResize);
        clearTimeout(resizeTimeout);
    });

    return {
        // Dimensions
        windowWidth,
        windowHeight,
        baseWidth,
        baseHeight,

        // Device detection
        isMobile,
        isTablet,
        isDesktop,
        isLandscape,
        isPortrait,
        currentBreakpoint,

        // Scaling
        widthScale,
        heightScale,
        averageScale,

        // Spacing and styles
        dynamicSpacing,
        responsiveStyles,

        // Breakpoints for external use
        BREAKPOINTS,
    };
}

