<template>
	<v-navigation-drawer
		v-model="drawerOpen"
		:rail="mini && !isMobile"
		:expand-on-hover="!isMobile"
		:width="isMobile ? '280' : '220'"
		:class="['drawer-custom', { 'drawer-visible': drawerOpen, 'mobile-drawer': isMobile, 'tablet-drawer': isTablet }]"
		@mouseleave="handleMouseLeave"
		:temporary="isMobile || isTablet"
		:permanent="isDesktop"
		location="left"
		:scrim="scrimColor"
		:touchless="false"
	>
		<!-- Mobile/Tablet Header -->
		<div v-if="isMobile || isTablet" class="drawer-header-mobile">
			<div class="d-flex align-center pa-4">
				<v-avatar :size="isMobile ? '48' : '40'">
					<v-img :src="companyImg" alt="Company logo" />
				</v-avatar>
				<div class="ml-3">
					<div class="drawer-company text-h6">{{ company }}</div>
					<div class="text-caption text-secondary">POS System</div>
				</div>
				<v-spacer />
				<v-btn
					v-if="isMobile"
					icon
					size="small"
					@click="drawerOpen = false"
					class="touch-target"
				>
					<v-icon>mdi-close</v-icon>
				</v-btn>
			</div>
		</div>

		<!-- Desktop Header -->
		<div v-else-if="!mini" class="drawer-header">
			<v-avatar size="40">
				<v-img :src="companyImg" alt="Company logo" />
			</v-avatar>
			<span class="drawer-company">{{ company }}</span>
		</div>
		<div v-else-if="isDesktop" class="drawer-header-mini">
			<v-avatar size="40">
				<v-img :src="companyImg" alt="Company logo" />
			</v-avatar>
		</div>

		<v-divider />

		<!-- Navigation List -->
		<v-list
			:density="isMobile ? 'default' : 'compact'"
			nav
			v-model:selected="activeItem"
			selected-class="active-item"
			class="drawer-list"
		>
			<v-list-item
				v-for="(item, index) in items"
				:key="item.text"
				:value="index"
				@click="changePage(item.text)"
				:class="['drawer-item', { 'mobile-item': isMobile }]"
				:min-height="isMobile ? '56' : '48'"
			>
				<template v-slot:prepend>
					<v-icon
						:class="['drawer-icon', { 'mobile-icon': isMobile }]"
						:size="isMobile ? '24' : '20'"
					>
						{{ item.icon }}
					</v-icon>
				</template>
				<v-list-item-title
					:class="['drawer-item-title', { 'mobile-title': isMobile }]"
				>
					{{ item.text }}
				</v-list-item-title>
			</v-list-item>
		</v-list>

		<!-- Mobile Footer with additional options -->
		<template v-if="isMobile">
			<v-divider class="mt-4" />
			<v-list density="compact" class="pb-4">
				<v-list-item @click="$emit('go-desk')" class="mobile-item">
					<template v-slot:prepend>
						<v-icon class="mobile-icon">mdi-view-dashboard</v-icon>
					</template>
					<v-list-item-title class="mobile-title">{{ __("Dashboard") }}</v-list-item-title>
				</v-list-item>
			</v-list>
		</template>
	</v-navigation-drawer>
</template>

<script>
import { useResponsive } from "../../composables/useResponsive.js";

export default {
	name: "NavbarDrawer",
	setup() {
		const { isMobile, isTablet, isDesktop } = useResponsive();
		return {
			isMobile,
			isTablet,
			isDesktop,
		};
	},
	props: {
		drawer: Boolean,
		company: String,
		companyImg: String,
		items: Array,
		item: Number,
		isDark: Boolean,
	},
	data() {
		return {
			mini: false,
			drawerOpen: this.drawer,
			activeItem: this.item,
			showSport: true,
		};
	},
	computed: {
		scrimColor() {
			// Use different scrim behavior based on device type
			if (this.isMobile || this.isTablet) {
				return this.isDark ? "rgba(0,0,0,0.7)" : "rgba(0,0,0,0.5)";
			}
			// Desktop behavior
			return this.isDark ? true : "rgba(255,255,255,1)";
		},
	},
	watch: {
		drawer(val) {
			this.drawerOpen = val;
			if (val && !this.isMobile) {
				this.mini = false;
			}
		},
		drawerOpen(val) {
			// Only prevent body scroll on mobile/tablet
			if (this.isMobile || this.isTablet) {
				document.body.style.overflow = val ? "hidden" : "";
			}
			this.$emit("update:drawer", val);
		},
		item(val) {
			this.activeItem = val;
		},
		activeItem(val) {
			this.$emit("update:item", val);
		},
		// Watch for device type changes
		isMobile(val) {
			if (val && this.drawerOpen) {
				// Convert to mobile drawer behavior
				this.mini = false;
			}
		},
	},
	mounted() {
		// Handle initial mobile state
		if (this.isMobile && this.drawerOpen) {
			this.mini = false;
		}
	},
	methods: {
		handleMouseLeave() {
			// Only auto-close on desktop
			if (this.isMobile || this.isTablet) return;
			if (!this.drawerOpen) return;

			clearTimeout(this._closeTimeout);
			this._closeTimeout = setTimeout(() => {
				this.drawerOpen = false;
				this.mini = true;
			}, 250);
		},
		changePage(key) {
			this.$emit("change-page", key);
			// Close drawer after selection on mobile/tablet
			if (this.isMobile || this.isTablet) {
				this.closeDrawer();
			}
		},
		closeDrawer() {
			this.drawerOpen = false;
			if (!this.isMobile) {
				this.mini = true;
			}
		},
	},
	emits: ["update:drawer", "update:item", "change-page", "go-desk"],
};
</script>

<style scoped>
/* Custom styling for the navigation drawer */
.drawer-custom {
	background-color: var(--surface-secondary, #ffffff);
	transition: var(--transition-normal, all 0.3s ease);
	z-index: 1005 !important; /* Higher than navbar but lower than dialogs */
}

/* Mobile Drawer Styling */
.mobile-drawer {
	z-index: 1010 !important;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

.tablet-drawer {
	z-index: 1008 !important;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* Mobile Header */
.drawer-header-mobile {
	background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	min-height: 80px;
}

/* Desktop Headers */
.drawer-header {
	display: flex;
	align-items: center;
	height: 64px;
	padding: 0 16px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.drawer-header-mini {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 64px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Company name styling */
.drawer-company {
	margin-left: 12px;
	flex: 1;
	font-weight: 500;
	font-size: 1rem;
	color: var(--text-primary, #333);
	font-family: "Roboto", sans-serif;
}

/* List styling */
.drawer-list {
	padding-top: 8px;
}

/* Icon styling */
.drawer-icon {
	font-size: 20px;
	color: var(--primary-start, #1976d2);
	transition: color 0.2s ease;
}

.mobile-icon {
	font-size: 24px !important;
}

/* Title styling */
.drawer-item-title {
	margin-left: 8px;
	font-weight: 500;
	font-size: 0.95rem;
	color: #000000 !important;
	font-family: "Roboto", sans-serif;
}

/* Mobile title styling */
.mobile-title {
	font-size: 1.1rem !important;
	font-weight: 500;
}

/* Mobile item styling */
.mobile-item {
	min-height: 56px !important;
	padding: 12px 16px !important;
	border-radius: 8px;
	margin: 4px 8px;
	transition: all 0.2s ease;
}

.mobile-item:hover {
	background-color: rgba(25, 118, 210, 0.08) !important;
	transform: translateX(4px);
}

/* Hover effect for all list items in the navigation drawer */
.v-list-item:hover {
	background-color: rgba(25, 118, 210, 0.08) !important;
}

.drawer-item {
	border-radius: 8px;
	margin: 2px 8px;
	transition: all 0.2s ease;
}

.drawer-item:hover {
	transform: translateX(2px);
}

/* Styling for the actively selected list item in the navigation drawer */
.active-item {
	background-color: rgba(25, 118, 210, 0.12) !important;
	border-right: 3px solid #1976d2;
}

/* Touch target improvements for mobile */
.touch-target {
	min-height: 48px;
	min-width: 48px;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* Dark Theme Adjustments */
:deep([data-theme="dark"]) .drawer-custom,
:deep(.v-theme--dark) .drawer-custom {
	background-color: var(--surface-primary, #1e1e1e) !important;
	color: var(--text-primary, #ffffff) !important;
}

:deep([data-theme="dark"]) .drawer-header,
:deep([data-theme="dark"]) .drawer-header-mini,
:deep(.v-theme--dark) .drawer-header,
:deep(.v-theme--dark) .drawer-header-mini {
	background: linear-gradient(135deg, #2d2d2d 0%, #1e1e1e 100%);
	border-bottom: 1px solid rgba(255, 255, 255, 0.12);
}

:deep([data-theme="dark"]) .drawer-item-title,
:deep(.v-theme--dark) .drawer-item-title {
	color: #000000 !important;
	font-weight: 500;
	font-size: 0.95rem;
	font-family: "Roboto", sans-serif;
}

:deep([data-theme="dark"]) .drawer-company,
:deep(.v-theme--dark) .drawer-company {
	color: var(--text-primary, #ffffff) !important;
	font-weight: 500;
	font-size: 1rem;
	font-family: "Roboto", sans-serif;
}

:deep([data-theme="dark"]) .drawer-icon,
:deep(.v-theme--dark) .drawer-icon {
	color: var(--primary-light, #90caf9) !important;
	font-size: 24px;
}

:deep([data-theme="dark"]) .v-list-item:hover,
:deep(.v-theme--dark) .v-list-item:hover {
	background-color: rgba(144, 202, 249, 0.08) !important;
}

:deep([data-theme="dark"]) .active-item,
:deep(.v-theme--dark) .active-item {
	background-color: rgba(144, 202, 249, 0.12) !important;
	border-right: 3px solid #90caf9;
}

:deep([data-theme="dark"]) .v-divider,
:deep(.v-theme--dark) .v-divider {
	border-color: rgba(255, 255, 255, 0.12) !important;
}

/* Hide drawer by default, show only when activated */
.drawer-custom {
	display: none !important;
}
.drawer-custom.drawer-visible {
	display: block !important;
}

/* Responsive adjustments for width and dark theme */
@media (max-width: 900px) and (orientation: landscape) {
	.drawer-custom.drawer-visible {
		width: 180px !important;
	}
}

@media (min-width: 601px) and (max-width: 1024px) {
	.drawer-custom.drawer-visible {
		width: 240px !important;
	}
}

@media (min-width: 1025px) {
	.drawer-custom.drawer-visible {
		width: 300px !important;
	}
}

@media (max-width: 1024px) {
	:deep([data-theme="dark"]) .drawer-custom.drawer-visible,
	:deep(.v-theme--dark) .drawer-custom.drawer-visible {
		background-color: var(--surface-primary, #1e1e1e) !important;
	}
}
</style>
