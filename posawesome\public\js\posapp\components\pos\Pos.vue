<template>
	<div class="pos-main-container dynamic-container" :style="responsiveStyles">
		<ClosingDialog></ClosingDialog>
		<Drafts></Drafts>
		<SalesOrders></SalesOrders>
		<Returns></Returns>
		<NewAddress></NewAddress>
		<MpesaPayments></MpesaPayments>
		<Variants></Variants>
		<OpeningDialog v-if="dialog" :dialog="dialog"></OpeningDialog>

		<!-- Mobile Layout -->
		<template v-if="isMobile && !dialog">
			<div class="mobile-pos-layout">
				<!-- Mobile Tab Navigation -->
				<v-tabs
					v-model="mobileActiveTab"
					class="mobile-tabs"
					color="primary"
					slider-color="primary"
					grow
				>
					<v-tab value="items" class="mobile-tab">
						<v-icon start>mdi-package-variant</v-icon>
						{{ __("Items") }}
					</v-tab>
					<v-tab value="invoice" class="mobile-tab">
						<v-icon start>mdi-receipt</v-icon>
						{{ __("Invoice") }}
						<v-badge
							v-if="invoiceItemsCount > 0"
							:content="invoiceItemsCount"
							color="error"
							inline
						/>
					</v-tab>
				</v-tabs>

				<!-- Mobile Tab Content -->
				<v-window v-model="mobileActiveTab" class="mobile-window">
					<v-window-item value="items" class="mobile-window-item">
						<div v-if="!payment && !showOffers && !coupons" class="mobile-content">
							<ItemsSelector></ItemsSelector>
						</div>
						<div v-else-if="showOffers" class="mobile-content">
							<PosOffers></PosOffers>
						</div>
						<div v-else-if="coupons" class="mobile-content">
							<PosCoupons></PosCoupons>
						</div>
						<div v-else-if="payment" class="mobile-content">
							<Payments></Payments>
						</div>
					</v-window-item>

					<v-window-item value="invoice" class="mobile-window-item">
						<div class="mobile-content">
							<Invoice></Invoice>
						</div>
					</v-window-item>
				</v-window>
			</div>
		</template>

		<!-- Tablet Layout -->
		<template v-else-if="isTablet && !dialog">
			<v-row dense class="ma-0 tablet-main-row">
				<v-col
					v-show="!payment && !showOffers && !coupons"
					md="6"
					sm="6"
					cols="12"
					class="pos tablet-col"
				>
					<ItemsSelector></ItemsSelector>
				</v-col>
				<v-col v-show="showOffers" md="6" sm="6" cols="12" class="pos tablet-col">
					<PosOffers></PosOffers>
				</v-col>
				<v-col v-show="coupons" md="6" sm="6" cols="12" class="pos tablet-col">
					<PosCoupons></PosCoupons>
				</v-col>
				<v-col v-show="payment" md="6" sm="6" cols="12" class="pos tablet-col">
					<Payments></Payments>
				</v-col>

				<v-col md="6" sm="6" cols="12" class="pos tablet-col">
					<Invoice></Invoice>
				</v-col>
			</v-row>
		</template>

		<!-- Desktop Layout -->
		<template v-else-if="!dialog">
			<v-row dense class="ma-0 desktop-main-row">
				<v-col
					v-show="!payment && !showOffers && !coupons"
					xl="5"
					lg="5"
					md="5"
					cols="12"
					class="pos desktop-col"
				>
					<ItemsSelector></ItemsSelector>
				</v-col>
				<v-col v-show="showOffers" xl="5" lg="5" md="5" cols="12" class="pos desktop-col">
					<PosOffers></PosOffers>
				</v-col>
				<v-col v-show="coupons" xl="5" lg="5" md="5" cols="12" class="pos desktop-col">
					<PosCoupons></PosCoupons>
				</v-col>
				<v-col v-show="payment" xl="5" lg="5" md="5" cols="12" class="pos desktop-col">
					<Payments></Payments>
				</v-col>

				<v-col xl="7" lg="7" md="7" cols="12" class="pos desktop-col">
					<Invoice></Invoice>
				</v-col>
			</v-row>
		</template>
	</div>
</template>

<script>
import ItemsSelector from "./ItemsSelector.vue";
import Invoice from "./Invoice.vue";
import OpeningDialog from "./OpeningDialog.vue";
import Payments from "./Payments.vue";
import PosOffers from "./PosOffers.vue";
import PosCoupons from "./PosCoupons.vue";
import Drafts from "./Drafts.vue";
import SalesOrders from "./SalesOrders.vue";
import ClosingDialog from "./ClosingDialog.vue";
import NewAddress from "./NewAddress.vue";
import Variants from "./Variants.vue";
import Returns from "./Returns.vue";
import MpesaPayments from "./Mpesa-Payments.vue";
import {
	getOpeningStorage,
	setOpeningStorage,
	clearOpeningStorage,
	initPromise,
	checkDbHealth,
	setTaxTemplate,
} from "../../../offline/index.js";
import { getCurrentInstance } from "vue";
import { usePosShift } from "../../composables/usePosShift.js";
import { useOffers } from "../../composables/useOffers.js";
// Import the cache cleanup function
import { clearExpiredCustomerBalances } from "../../../offline/index.js";
import { useResponsive } from "../../composables/useResponsive.js";

export default {
	setup() {
		const instance = getCurrentInstance();
		const responsive = useResponsive();
		const shift = usePosShift(() => {
			if (instance && instance.proxy) {
				instance.proxy.dialog = true;
			}
		});
		const offers = useOffers();
		return { ...responsive, ...shift, ...offers };
	},
	data: function () {
		return {
			dialog: false,
			payment: false,
			showOffers: false,
			coupons: false,
			itemsLoaded: false,
			customersLoaded: false,
			// Mobile-specific data
			mobileActiveTab: "items",
		};
	},

	computed: {
		invoiceItemsCount() {
			// This should be connected to the actual invoice items count
			// For now, return a placeholder
			return this.$store?.state?.invoice?.items?.length || 0;
		},
	},

	components: {
		ItemsSelector,
		Invoice,
		OpeningDialog,
		Payments,
		Drafts,
		ClosingDialog,

		Returns,
		PosOffers,
		PosCoupons,
		NewAddress,
		Variants,
		MpesaPayments,
		SalesOrders,
	},

	methods: {
		create_opening_voucher() {
			this.dialog = true;
		},
		get_pos_setting() {
			frappe.db.get_doc("POS Settings", undefined).then((doc) => {
				this.eventBus.emit("set_pos_settings", doc);
			});
		},
		checkLoadingComplete() {
			if (this.itemsLoaded && this.customersLoaded) {
				console.info("Loading completed");
			}
		},
	},

	mounted: function () {
		this.$nextTick(function () {
			this.check_opening_entry();
			this.get_pos_setting();
			this.eventBus.on("close_opening_dialog", () => {
				this.dialog = false;
			});
			this.eventBus.on("register_pos_data", (data) => {
				this.pos_profile = data.pos_profile;
				this.get_offers(this.pos_profile.name, this.pos_profile);
				this.pos_opening_shift = data.pos_opening_shift;
				this.eventBus.emit("register_pos_profile", data);
				console.info("LoadPosProfile");
			});
			// When profile is registered directly from composables,
			// ensure offers are fetched as well
			this.eventBus.on("register_pos_profile", (data) => {
				if (data && data.pos_profile) {
					this.get_offers(data.pos_profile.name, data.pos_profile);
				}
			});
			this.eventBus.on("show_payment", (data) => {
				this.payment = data === "true";
				this.showOffers = false;
				this.coupons = false;
			});
			this.eventBus.on("show_offers", (data) => {
				this.showOffers = data === "true";
				this.payment = false;
				this.coupons = false;
			});
			this.eventBus.on("show_coupons", (data) => {
				this.coupons = data === "true";
				this.showOffers = false;
				this.payment = false;
			});
			this.eventBus.on("open_closing_dialog", () => {
				this.get_closing_data();
			});
			this.eventBus.on("submit_closing_pos", (data) => {
				this.submit_closing_pos(data);
			});

			this.eventBus.on("items_loaded", () => {
				this.itemsLoaded = true;
				this.checkLoadingComplete();
			});
			this.eventBus.on("customers_loaded", () => {
				this.customersLoaded = true;
				this.checkLoadingComplete();
			});
		});
	},
	beforeUnmount() {
		this.eventBus.off("close_opening_dialog");
		this.eventBus.off("register_pos_data");
		this.eventBus.off("register_pos_profile");
		this.eventBus.off("LoadPosProfile");
		this.eventBus.off("show_offers");
		this.eventBus.off("show_coupons");
		this.eventBus.off("open_closing_dialog");
		this.eventBus.off("submit_closing_pos");
		this.eventBus.off("items_loaded");
		this.eventBus.off("customers_loaded");
	},
	// In the created() or mounted() lifecycle hook
	created() {
		// Clean up expired customer balance cache on POS load
		clearExpiredCustomerBalances();
	},
};
</script>

<style scoped>
.pos-main-container {
	height: calc(100dvh - 56px);
	overflow: hidden;
	background: var(--surface-primary, #f5f5f5);
	padding-top: var(--dynamic-md);
}

/* Mobile Layout Styles */
.mobile-pos-layout {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.mobile-tabs {
	flex-shrink: 0;
	background: white;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	z-index: 10;
}

.mobile-tab {
	min-height: 56px !important;
	font-size: 0.9rem;
	font-weight: 500;
}

.mobile-window {
	flex: 1;
	overflow: hidden;
}

.mobile-window-item {
	height: 100%;
}

.mobile-content {
	height: 100%;
	padding: 8px;
	overflow: auto;
}

/* Tablet Layout Styles */
.tablet-main-row {
	padding: 0;
	margin: 0;
	height: 100%;
}

.tablet-col {
	padding: var(--dynamic-sm);
	transition: padding 0.3s ease;
	margin-top: var(--dynamic-sm);
}

/* Desktop Layout Styles */
.desktop-main-row {
	padding: 0;
	margin: 0;
	height: 100%;
}

.desktop-col {
	padding: var(--dynamic-sm);
	transition: padding 0.3s ease;
	margin-top: var(--dynamic-sm);
}

/* Legacy support */
.dynamic-main-row {
	padding: 0;
	margin: 0;
}

.dynamic-col {
	padding: var(--dynamic-sm);
	transition: padding 0.3s ease;
	margin-top: var(--dynamic-sm);
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
	.pos-main-container {
		height: calc(100dvh - 64px); /* Mobile navbar height */
		padding-top: 0;
	}

	.mobile-content {
		padding: 4px;
	}
}

@media (min-width: 768px) and (max-width: 991.98px) {
	.pos-main-container {
		height: calc(100dvh - 56px);
		padding-top: var(--dynamic-sm);
	}

	.tablet-col {
		padding: var(--dynamic-xs);
		margin-top: var(--dynamic-xs);
	}
}

@media (min-width: 992px) {
	.pos-main-container {
		height: calc(100dvh - 56px);
		padding-top: var(--dynamic-md);
	}
}
</style>
