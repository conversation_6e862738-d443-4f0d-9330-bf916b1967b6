/**
 * POS Awesome V15 - Responsive Performance Optimization
 * تحسينات الأداء للتصميم المتجاوب
 */

// Performance monitoring utilities
class ResponsivePerformanceMonitor {
    constructor() {
        this.metrics = {
            resizeEvents: 0,
            renderTime: [],
            memoryUsage: [],
            layoutShifts: 0
        };
        
        this.init();
    }
    
    init() {
        this.setupResizeThrottling();
        this.setupPerformanceObserver();
        this.setupMemoryMonitoring();
        this.logInitialMetrics();
    }
    
    // تحسين أحداث تغيير حجم النافذة
    setupResizeThrottling() {
        let resizeTimeout;
        let lastResize = 0;
        
        const throttledResize = () => {
            const now = Date.now();
            if (now - lastResize < 16) return; // 60fps limit
            
            lastResize = now;
            this.metrics.resizeEvents++;
            
            // إرسال حدث مخصص للمكونات
            window.dispatchEvent(new CustomEvent('optimizedResize', {
                detail: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    timestamp: now
                }
            }));
        };
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(throttledResize, 16);
        });
    }
    
    // مراقبة أداء الرسم
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // مراقبة Layout Shift
            const layoutShiftObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.value > 0.1) { // CLS threshold
                        this.metrics.layoutShifts++;
                        console.warn('Layout shift detected:', entry.value);
                    }
                }
            });
            
            try {
                layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
            } catch (e) {
                console.log('Layout shift monitoring not supported');
            }
            
            // مراقبة Paint timing
            const paintObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.metrics.renderTime.push({
                        name: entry.name,
                        startTime: entry.startTime,
                        duration: entry.duration
                    });
                }
            });
            
            try {
                paintObserver.observe({ entryTypes: ['paint', 'measure'] });
            } catch (e) {
                console.log('Paint timing monitoring not supported');
            }
        }
    }
    
    // مراقبة استخدام الذاكرة
    setupMemoryMonitoring() {
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.memoryUsage.push({
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                });
                
                // الاحتفاظ بآخر 100 قياس فقط
                if (this.metrics.memoryUsage.length > 100) {
                    this.metrics.memoryUsage.shift();
                }
            }, 5000); // كل 5 ثوان
        }
    }
    
    // تسجيل المقاييس الأولية
    logInitialMetrics() {
        console.group('🚀 POS Awesome V15 - Responsive Performance Metrics');
        console.log('Screen size:', `${window.innerWidth}x${window.innerHeight}`);
        console.log('Device pixel ratio:', window.devicePixelRatio);
        console.log('User agent:', navigator.userAgent);
        
        if ('connection' in navigator) {
            console.log('Network:', navigator.connection.effectiveType);
        }
        
        console.groupEnd();
    }
    
    // الحصول على تقرير الأداء
    getPerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            screen: {
                width: window.innerWidth,
                height: window.innerHeight,
                pixelRatio: window.devicePixelRatio
            },
            metrics: {
                resizeEvents: this.metrics.resizeEvents,
                layoutShifts: this.metrics.layoutShifts,
                averageRenderTime: this.calculateAverageRenderTime(),
                memoryTrend: this.getMemoryTrend()
            },
            recommendations: this.generateRecommendations()
        };
        
        return report;
    }
    
    calculateAverageRenderTime() {
        if (this.metrics.renderTime.length === 0) return 0;
        
        const total = this.metrics.renderTime.reduce((sum, entry) => sum + entry.duration, 0);
        return total / this.metrics.renderTime.length;
    }
    
    getMemoryTrend() {
        if (this.metrics.memoryUsage.length < 2) return 'insufficient_data';
        
        const recent = this.metrics.memoryUsage.slice(-10);
        const first = recent[0].used;
        const last = recent[recent.length - 1].used;
        
        if (last > first * 1.2) return 'increasing';
        if (last < first * 0.8) return 'decreasing';
        return 'stable';
    }
    
    generateRecommendations() {
        const recommendations = [];
        
        if (this.metrics.layoutShifts > 5) {
            recommendations.push('Consider reducing layout shifts by setting explicit dimensions');
        }
        
        if (this.metrics.resizeEvents > 100) {
            recommendations.push('High resize event count - consider debouncing resize handlers');
        }
        
        const avgRenderTime = this.calculateAverageRenderTime();
        if (avgRenderTime > 16) {
            recommendations.push('Render time exceeds 16ms - consider optimizing animations');
        }
        
        if (this.getMemoryTrend() === 'increasing') {
            recommendations.push('Memory usage is increasing - check for memory leaks');
        }
        
        return recommendations;
    }
}

// CSS optimization utilities
class ResponsiveCSSOptimizer {
    constructor() {
        this.criticalCSS = new Set();
        this.unusedCSS = new Set();
        this.init();
    }
    
    init() {
        this.identifyCriticalCSS();
        this.setupIntersectionObserver();
    }
    
    // تحديد CSS الحرج
    identifyCriticalCSS() {
        const criticalSelectors = [
            '.v-application',
            '.v-main',
            '.pos-main-container',
            '.v-navigation-drawer',
            '.v-app-bar',
            '.mobile-*',
            '.tablet-*',
            '.desktop-*'
        ];
        
        criticalSelectors.forEach(selector => {
            this.criticalCSS.add(selector);
        });
    }
    
    // مراقبة العناصر المرئية
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // تحميل CSS إضافي عند الحاجة
                        this.loadConditionalCSS(entry.target);
                    }
                });
            });
            
            // مراقبة العناصر التي قد تحتاج CSS إضافي
            document.querySelectorAll('.v-dialog, .v-menu, .v-tooltip').forEach(el => {
                observer.observe(el);
            });
        }
    }
    
    loadConditionalCSS(element) {
        // تحميل CSS حسب الحاجة
        if (element.classList.contains('v-dialog')) {
            this.ensureDialogCSS();
        }
    }
    
    ensureDialogCSS() {
        if (!document.querySelector('#dialog-css')) {
            const link = document.createElement('link');
            link.id = 'dialog-css';
            link.rel = 'stylesheet';
            link.href = '/assets/posawesome/css/dialogs.css';
            document.head.appendChild(link);
        }
    }
}

// Image optimization for responsive design
class ResponsiveImageOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupLazyLoading();
        this.optimizeImages();
    }
    
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
        }
    }
    
    optimizeImages() {
        // تحسين الصور حسب حجم الشاشة
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.srcset && img.dataset.responsive) {
                this.generateResponsiveSrcset(img);
            }
        });
    }
    
    generateResponsiveSrcset(img) {
        const baseSrc = img.src;
        const sizes = [320, 640, 768, 1024, 1200];
        
        const srcset = sizes.map(size => {
            return `${baseSrc}?w=${size} ${size}w`;
        }).join(', ');
        
        img.srcset = srcset;
        img.sizes = '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw';
    }
}

// Main performance manager
class ResponsivePerformanceManager {
    constructor() {
        this.monitor = new ResponsivePerformanceMonitor();
        this.cssOptimizer = new ResponsiveCSSOptimizer();
        this.imageOptimizer = new ResponsiveImageOptimizer();
        
        this.setupGlobalOptimizations();
    }
    
    setupGlobalOptimizations() {
        // تحسين الأداء العام
        this.enableGPUAcceleration();
        this.optimizeScrolling();
        this.setupPreloadHints();
    }
    
    enableGPUAcceleration() {
        const style = document.createElement('style');
        style.textContent = `
            .v-dialog,
            .v-navigation-drawer,
            .v-app-bar,
            .pos-main-container {
                transform: translateZ(0);
                will-change: transform;
            }
        `;
        document.head.appendChild(style);
    }
    
    optimizeScrolling() {
        // تحسين التمرير
        document.addEventListener('touchstart', () => {}, { passive: true });
        document.addEventListener('touchmove', () => {}, { passive: true });
    }
    
    setupPreloadHints() {
        // إضافة تلميحات التحميل المسبق
        const preloadLinks = [
            '/assets/posawesome/css/responsive.css',
            '/assets/posawesome/js/posapp/composables/useResponsive.js'
        ];
        
        preloadLinks.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = href;
            link.as = href.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }
    
    // الحصول على تقرير شامل
    getFullReport() {
        return {
            performance: this.monitor.getPerformanceReport(),
            timestamp: new Date().toISOString(),
            version: 'POS Awesome V15 Responsive'
        };
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.ResponsivePerformanceManager = ResponsivePerformanceManager;
    
    // تشغيل تلقائي
    document.addEventListener('DOMContentLoaded', () => {
        window.posResponsivePerformance = new ResponsivePerformanceManager();
        console.log('✅ Responsive Performance Manager initialized');
    });
}

export { ResponsivePerformanceManager, ResponsivePerformanceMonitor };
