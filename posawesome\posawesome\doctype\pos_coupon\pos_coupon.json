{"actions": [], "allow_import": 1, "autoname": "field:coupon_name", "creation": "2021-07-24 01:47:32.743719", "doctype": "DocType", "document_type": "Other", "editable_grid": 1, "engine": "InnoDB", "field_order": ["coupon_name", "coupon_type", "customer", "customer_name", "mobile_no", "email_id", "column_break_4", "coupon_code", "company", "campaign", "pos_offer", "referral_code", "uses", "valid_from", "valid_upto", "maximum_use", "used", "one_use", "column_break_11", "description"], "fields": [{"description": "e.g. \"Summer Holiday 2019 Offer 20\"", "fieldname": "coupon_name", "fieldtype": "Data", "label": "Coupon Name", "reqd": 1, "unique": 1}, {"fieldname": "coupon_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Coupon Type", "options": "Promotional\nGift Card", "reqd": 1}, {"depends_on": "eval: doc.coupon_type == \"Gift Card\"", "fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Customer", "options": "Customer"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"description": "unique e.g. SAVE20  To be used to get discount", "fieldname": "coupon_code", "fieldtype": "Data", "in_standard_filter": 1, "label": "Coupon Code", "no_copy": 1, "set_only_once": 1, "unique": 1}, {"fieldname": "pos_offer", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "uses", "fieldtype": "Section Break", "label": "Validity and Usage"}, {"fieldname": "valid_from", "fieldtype": "Date", "in_list_view": 1, "label": "<PERSON><PERSON>"}, {"fieldname": "valid_upto", "fieldtype": "Date", "label": "<PERSON><PERSON>"}, {"depends_on": "eval: doc.coupon_type == \"Promotional\"", "fieldname": "maximum_use", "fieldtype": "Int", "label": "Maximum Use"}, {"default": "0", "fieldname": "used", "fieldtype": "Int", "in_list_view": 1, "label": "Used", "no_copy": 1, "read_only": 1}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fetch_from": "pos_offer.description", "fetch_if_empty": 1, "fieldname": "description", "fieldtype": "Text Editor", "label": "Coupon Description"}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "campaign", "fieldtype": "Link", "in_standard_filter": 1, "label": "Campaign", "options": "Campaign"}, {"default": "0", "fieldname": "one_use", "fieldtype": "Check", "label": "Only One Use Per Customer"}, {"depends_on": "customer", "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "label": "Customer Name", "read_only": 1}, {"depends_on": "customer", "fetch_from": "customer.mobile_no", "fetch_if_empty": 1, "fieldname": "mobile_no", "fieldtype": "Data", "label": "Mobile NO", "options": "Phone"}, {"depends_on": "customer", "fetch_from": "customer.email_id", "fetch_if_empty": 1, "fieldname": "email_id", "fieldtype": "Data", "label": "Email ID", "options": "Email"}, {"fieldname": "referral_code", "fieldtype": "Link", "label": "Referral Code", "options": "Referral Code", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2021-07-30 00:17:17.711972", "modified_by": "Administrator", "module": "POSAwesome", "name": "POS Coupon", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "title_field": "coupon_code", "track_changes": 1}