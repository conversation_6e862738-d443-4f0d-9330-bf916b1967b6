<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Awesome V15 - Performance Test</title>
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.7.5/dist/vuetify.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="./css/responsive.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .performance-dashboard {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border-left: 4px solid #1976d2;
        }
        
        .metric-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }
        
        .metric-unit {
            font-size: 0.9rem;
            color: #666;
        }
        
        .status-good { color: #4caf50; }
        .status-warning { color: #ff9800; }
        .status-error { color: #f44336; }
        
        .test-controls {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 1rem;
            transition: all 0.2s;
        }
        
        .test-button:hover {
            background: #1565c0;
            transform: translateY(-1px);
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1976d2, #42a5f5);
            transition: width 0.3s ease;
        }
        
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .recommendations h4 {
            color: #856404;
            margin-top: 0;
        }
        
        .recommendations ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .recommendations li {
            margin-bottom: 5px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h1>🚀 POS Awesome V15 - اختبار الأداء</h1>
        <p>مراقبة وتحليل أداء التصميم المتجاوب في الوقت الفعلي</p>
        
        <button class="test-button" onclick="startPerformanceTest()">بدء اختبار الأداء</button>
        <button class="test-button" onclick="simulateResize()">محاكاة تغيير الحجم</button>
        <button class="test-button" onclick="stressTest()">اختبار الضغط</button>
        <button class="test-button" onclick="generateReport()">تقرير شامل</button>
        <button class="test-button" onclick="clearLogs()">مسح السجلات</button>
    </div>
    
    <div class="performance-dashboard">
        <div class="metric-card">
            <div class="metric-title">FPS (إطار في الثانية)</div>
            <div class="metric-value" id="fps-value">--</div>
            <div class="metric-unit">fps</div>
            <div class="progress-bar">
                <div class="progress-fill" id="fps-progress" style="width: 0%"></div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">استخدام الذاكرة</div>
            <div class="metric-value" id="memory-value">--</div>
            <div class="metric-unit">MB</div>
            <div class="progress-bar">
                <div class="progress-fill" id="memory-progress" style="width: 0%"></div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">Layout Shifts</div>
            <div class="metric-value" id="cls-value">--</div>
            <div class="metric-unit">CLS Score</div>
            <div class="progress-bar">
                <div class="progress-fill" id="cls-progress" style="width: 0%"></div>
            </div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">أحداث Resize</div>
            <div class="metric-value" id="resize-value">0</div>
            <div class="metric-unit">events</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">وقت الاستجابة</div>
            <div class="metric-value" id="response-value">--</div>
            <div class="metric-unit">ms</div>
        </div>
        
        <div class="metric-card">
            <div class="metric-title">حالة الشبكة</div>
            <div class="metric-value" id="network-value">--</div>
            <div class="metric-unit" id="network-type">--</div>
        </div>
    </div>
    
    <div class="recommendations" id="recommendations" style="display: none;">
        <h4>🎯 توصيات التحسين</h4>
        <ul id="recommendations-list"></ul>
    </div>
    
    <div class="log-container" id="performance-log">
        <div>📊 Performance Monitor Ready - جاهز لمراقبة الأداء</div>
        <div>💡 استخدم الأزرار أعلاه لبدء الاختبارات</div>
    </div>
    
    <script type="module">
        // Performance monitoring variables
        let performanceData = {
            fps: 0,
            memory: 0,
            cls: 0,
            resizeEvents: 0,
            responseTime: 0,
            startTime: Date.now()
        };
        
        let frameCount = 0;
        let lastTime = Date.now();
        let isMonitoring = false;
        
        // FPS monitoring
        function measureFPS() {
            frameCount++;
            const currentTime = Date.now();
            
            if (currentTime - lastTime >= 1000) {
                performanceData.fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                updateFPSDisplay();
            }
            
            if (isMonitoring) {
                requestAnimationFrame(measureFPS);
            }
        }
        
        // Memory monitoring
        function measureMemory() {
            if ('memory' in performance) {
                performanceData.memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                updateMemoryDisplay();
            }
        }
        
        // Network monitoring
        function measureNetwork() {
            if ('connection' in navigator) {
                const connection = navigator.connection;
                document.getElementById('network-value').textContent = connection.effectiveType || 'unknown';
                document.getElementById('network-type').textContent = connection.type || '';
            }
        }
        
        // Update displays
        function updateFPSDisplay() {
            const fpsElement = document.getElementById('fps-value');
            const progressElement = document.getElementById('fps-progress');
            
            fpsElement.textContent = performanceData.fps;
            fpsElement.className = performanceData.fps >= 50 ? 'metric-value status-good' : 
                                  performanceData.fps >= 30 ? 'metric-value status-warning' : 
                                  'metric-value status-error';
            
            progressElement.style.width = Math.min(performanceData.fps / 60 * 100, 100) + '%';
        }
        
        function updateMemoryDisplay() {
            const memoryElement = document.getElementById('memory-value');
            const progressElement = document.getElementById('memory-progress');
            
            memoryElement.textContent = performanceData.memory;
            memoryElement.className = performanceData.memory <= 50 ? 'metric-value status-good' : 
                                     performanceData.memory <= 100 ? 'metric-value status-warning' : 
                                     'metric-value status-error';
            
            progressElement.style.width = Math.min(performanceData.memory / 200 * 100, 100) + '%';
        }
        
        // Logging function
        function log(message, type = 'info') {
            const logContainer = document.getElementById('performance-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            
            const icon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : '📊';
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Test functions
        window.startPerformanceTest = function() {
            isMonitoring = true;
            log('بدء مراقبة الأداء...', 'success');
            
            measureFPS();
            
            setInterval(() => {
                measureMemory();
                measureNetwork();
            }, 1000);
            
            log('تم تفعيل مراقبة FPS والذاكرة والشبكة');
        };
        
        window.simulateResize = function() {
            log('محاكاة أحداث تغيير الحجم...', 'warning');
            
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    window.dispatchEvent(new Event('resize'));
                    performanceData.resizeEvents++;
                    document.getElementById('resize-value').textContent = performanceData.resizeEvents;
                    log(`حدث resize رقم ${performanceData.resizeEvents}`);
                }, i * 100);
            }
        };
        
        window.stressTest = function() {
            log('بدء اختبار الضغط...', 'warning');
            
            // إنشاء عناصر DOM كثيرة
            const container = document.createElement('div');
            container.style.position = 'absolute';
            container.style.top = '-9999px';
            
            for (let i = 0; i < 1000; i++) {
                const element = document.createElement('div');
                element.className = 'test-element';
                element.style.width = Math.random() * 100 + 'px';
                element.style.height = Math.random() * 100 + 'px';
                element.style.background = `hsl(${Math.random() * 360}, 50%, 50%)`;
                container.appendChild(element);
            }
            
            document.body.appendChild(container);
            
            setTimeout(() => {
                document.body.removeChild(container);
                log('انتهى اختبار الضغط', 'success');
            }, 5000);
            
            log('تم إنشاء 1000 عنصر DOM لاختبار الأداء');
        };
        
        window.generateReport = function() {
            log('إنشاء تقرير الأداء...', 'info');
            
            const report = {
                timestamp: new Date().toISOString(),
                metrics: performanceData,
                screen: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    pixelRatio: window.devicePixelRatio
                },
                userAgent: navigator.userAgent
            };
            
            // عرض التوصيات
            const recommendations = [];
            
            if (performanceData.fps < 30) {
                recommendations.push('FPS منخفض - قم بتحسين الرسوميات والحركات');
            }
            
            if (performanceData.memory > 100) {
                recommendations.push('استخدام ذاكرة عالي - تحقق من تسريبات الذاكرة');
            }
            
            if (performanceData.resizeEvents > 50) {
                recommendations.push('أحداث resize كثيرة - استخدم throttling');
            }
            
            if (recommendations.length > 0) {
                const recContainer = document.getElementById('recommendations');
                const recList = document.getElementById('recommendations-list');
                
                recList.innerHTML = '';
                recommendations.forEach(rec => {
                    const li = document.createElement('li');
                    li.textContent = rec;
                    recList.appendChild(li);
                });
                
                recContainer.style.display = 'block';
            }
            
            console.log('Performance Report:', report);
            log('تم إنشاء التقرير - راجع وحدة التحكم للتفاصيل', 'success');
        };
        
        window.clearLogs = function() {
            document.getElementById('performance-log').innerHTML = '';
            log('تم مسح السجلات', 'info');
        };
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة اختبار الأداء', 'success');
            measureNetwork();
        });
        
        // Monitor resize events
        window.addEventListener('resize', function() {
            performanceData.resizeEvents++;
            document.getElementById('resize-value').textContent = performanceData.resizeEvents;
        });
    </script>
</body>
</html>
